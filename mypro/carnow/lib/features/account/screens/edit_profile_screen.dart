import 'package:flutter/material.dart';
import '../../../core/utils/unified_logger.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../../../core/providers/users_provider.dart';
// DEPRECATED: import '../../../core/auth/auth_providers.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/models/city_model.dart';
import '../../../shared/providers/location_provider.dart';
import '../../../shared/widgets/primary_button.dart';
import '../../../core/widgets/loading_indicators.dart';
import '../providers/account_provider.dart';

/// شاشة تعديل الملف الشخصي للمستخدمين المسجلين
class EditProfileScreen extends HookConsumerWidget {
  const EditProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final currentUser = ref.watch(currentUserProvider);
    final currentUserProfile = ref.watch(currentUserStreamProvider).value;
    
    // Loading states
    final isLoading = useState<bool>(false);
    final errorMessage = useState<String?>(null);

    // Form controllers
    final nameController = useTextEditingController();
    final emailController = useTextEditingController();
    final phoneController = useTextEditingController();
    final addressController = useTextEditingController();
    
    // Form state
    final formKey = useState<GlobalKey<FormState>>(GlobalKey<FormState>());
    final selectedCityId = useState<int?>(null);
    final hasChanges = useState<bool>(false);

    // Initialize form with current user data
    useEffect(() {
      if (currentUser != null) {
        UnifiedLogger.info('Initializing form with user data: ${currentUser.email}');
        emailController.text = currentUser.email ?? '';
      }
      
      if (currentUserProfile != null) {
        nameController.text = currentUserProfile.name ?? '';
        phoneController.text = currentUserProfile.phone ?? '';
        addressController.text = currentUserProfile.address ?? '';
        selectedCityId.value = currentUserProfile.cityId;
      }
      return null;
    }, [currentUser, currentUserProfile]);

    // Listen for profile updates and refresh form fields automatically
    useEffect(() {
      if (currentUserProfile != null) {
        // Update form fields when profile data changes (e.g., after save)
        nameController.text = currentUserProfile.name ?? '';
        phoneController.text = currentUserProfile.phone ?? '';
        addressController.text = currentUserProfile.address ?? '';
        selectedCityId.value = currentUserProfile.cityId;
        hasChanges.value = false; // Reset changes flag
      }
      return null;
    }, [currentUserProfile]);

    // Save profile function
    Future<void> saveProfile() async {
      if (currentUser == null) {
        errorMessage.value = 'المستخدم غير مسجل دخول';
        return;
      }

      isLoading.value = true;
      errorMessage.value = null;

      try {
        // Update user profile in database
        await ref.read(accountNotifierProvider.notifier).updateUserProfile(
          name: nameController.text.trim(),
          phone: phoneController.text.trim(),
          address: addressController.text.trim(),
          cityId: selectedCityId.value,
        );

        // Refresh the provider to get updated data
        // The useEffect will automatically update form fields when currentUserProfile changes
        ref.invalidate(currentUserStreamProvider);
        
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حفظ التغييرات بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        errorMessage.value = 'خطأ في حفظ البيانات: ${e.toString()}';
      } finally {
        isLoading.value = false;
      }
    }

    // Show error if any
    if (errorMessage.value != null) {
      return Scaffold(
        appBar: AppBar(title: const Text('تعديل الملف الشخصي')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                errorMessage.value!,
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.error,
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => errorMessage.value = null,
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    // Show loading if user not loaded
    if (currentUser == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('تعديل الملف الشخصي')),
        body: Center(child: LoadingIndicators.primary()),
      );
    }

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: const Text('تعديل الملف الشخصي'),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: isLoading.value ? null : saveProfile,
            child: isLoading.value 
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('حفظ'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Form(
          key: formKey.value,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // معلومات المستخدم الحالي
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(AppTheme.spacingM),
                  child: Column(
                    children: [
                      CircleAvatar(
                        radius: 40,
                        backgroundColor: theme.colorScheme.primary
                            .withValues(alpha: 0.10),
                        child: Text(
                          (currentUser.email.isNotEmpty)
                              ? currentUser.email.substring(0, 1).toUpperCase()
                              : '؟',
                          style: theme.textTheme.headlineMedium?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingS),
                      Text(
                        currentUserProfile?.name ?? 'بدون اسم',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (currentUser.email.isNotEmpty)
                        Text(
                          currentUser.email,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(
                              alpha: 0.70,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: AppTheme.spacingL),

              // البيانات الشخصية
              Text(
                'البيانات الشخصية',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
              const SizedBox(height: AppTheme.spacingM),

              // حقل البريد الإلكتروني (للقراءة فقط)
              TextFormField(
                controller: emailController,
                enabled: false,
                decoration: InputDecoration(
                  labelText: 'البريد الإلكتروني',
                  prefixIcon: const Icon(Icons.email_outlined),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  fillColor: theme.colorScheme.onSurface.withValues(
                    alpha: 0.05,
                  ),
                ),
              ),
              const SizedBox(height: AppTheme.spacingM),

              // حقل الاسم
              TextFormField(
                controller: nameController,
                onChanged: (value) => hasChanges.value = true,
                decoration: InputDecoration(
                  labelText: 'الاسم الكامل',
                  hintText: 'أدخل اسمك الكامل',
                  prefixIcon: const Icon(Icons.person_outline),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                textCapitalization: TextCapitalization.words,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'الاسم مطلوب';
                  }
                  if (value.trim().length < 2) {
                    return 'الاسم قصير جداً';
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppTheme.spacingM),

              // حقل رقم الهاتف
              TextFormField(
                controller: phoneController,
                onChanged: (value) => hasChanges.value = true,
                decoration: InputDecoration(
                  labelText: 'رقم الهاتف',
                  hintText: 'أدخل رقم هاتفك',
                  prefixIcon: const Icon(Icons.phone_outlined),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                keyboardType: TextInputType.phone,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                validator: (value) {
                  if (value != null &&
                      value.isNotEmpty &&
                      value.length < 8) {
                    return 'رقم هاتف غير صالح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppTheme.spacingM),

              // City Dropdown
              Consumer(
                builder: (context, ref, child) {
                  final citiesAsync = ref.watch(citiesProvider);
                  return citiesAsync.when(
                    loading: () => Center(child: LoadingIndicators.primary()),
                    error: (err, stack) => Center(child: Text('Error loading cities: $err')),
                    data: (cities) {
                      return DropdownButtonFormField<int>(
                        value: selectedCityId.value,
                        items: cities.map((City city) {
                          return DropdownMenuItem<int>(
                            value: city.id,
                            child: Text(city.nameArabic),
                          );
                        }).toList(),
                        onChanged: (int? newValue) {
                          selectedCityId.value = newValue;
                          hasChanges.value = true;
                        },
                        decoration: InputDecoration(
                          labelText: 'المدينة',
                          prefixIcon: const Icon(
                            Icons.location_city_outlined,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
              const SizedBox(height: AppTheme.spacingM),

              // Detailed Address
              TextFormField(
                controller: addressController,
                onChanged: (value) => hasChanges.value = true,
                decoration: InputDecoration(
                  labelText: 'العنوان التفصيلي',
                  hintText: 'أدخل عنوانك التفصيلي',
                  prefixIcon: const Icon(Icons.maps_home_work_outlined),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),

              const SizedBox(height: AppTheme.spacingL),

              // زر حفظ التغييرات
              PrimaryButton(
                onPressed: !isLoading.value ? saveProfile : null,
                text: 'حفظ التغييرات',
              ),

              const SizedBox(height: AppTheme.spacingL),

              // قسم تغيير كلمة المرور
              Text(
                'الأمان',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
              const SizedBox(height: AppTheme.spacingM),
              ListTile(
                leading: const Icon(Icons.lock_outline),
                title: const Text('تغيير كلمة المرور'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  // TODO: Implement navigation to change password screen
                },
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                tileColor: theme.colorScheme.surface,
              ),

              const SizedBox(height: AppTheme.spacingL),

              // قسم حذف الحساب
              ListTile(
                leading: Icon(
                  Icons.delete_outline,
                  color: theme.colorScheme.error,
                ),
                title: Text(
                  'حذف الحساب',
                  style: TextStyle(color: theme.colorScheme.error),
                ),
                onTap: () => _showDeleteConfirmationDialog(context, ref),
                shape: RoundedRectangleBorder(
                  side: BorderSide(
                    color: theme.colorScheme.error.withValues(alpha: 0.20),
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDeleteConfirmationDialog(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تأكيد حذف الحساب'),
          content: const Text(
            'هل أنت متأكد من رغبتك في حذف حسابك؟ هذا الإجراء لا يمكن التراجع عنه.',
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            FilledButton(
              onPressed: () async {
                Navigator.of(context).pop(); // Close the dialog
                await _deleteAccount(context, ref);
              },
              style: FilledButton.styleFrom(
                backgroundColor: theme.colorScheme.error,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteAccount(BuildContext context, WidgetRef ref) async {
    try {
      final currentUser = ref.read(currentUserProvider);
      if (currentUser?.id == null) throw Exception('User not authenticated');

      // TODO: Implement account deletion through Go backend

      // Sign out the user after deleting their account data
                final authProvider = ref.read(simpleSupabaseAuthProvider.notifier);
          await authProvider.signOut();

      if (context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('تم حذف الحساب بنجاح.')));
        // Navigate to the login/home screen after account deletion
        context.go('/login');
      }
    } catch (error) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل حذف الحساب: ${error.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}
