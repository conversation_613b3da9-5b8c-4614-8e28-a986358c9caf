import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../../../core/auth/auth_models.dart';
import '../models/user_model.dart';

part 'auth_providers.g.dart';

@riverpod
class NativeGoogleAuthNotifier extends _$NativeGoogleAuthNotifier {
  @override
  Stream<User?> build() {
    // Use UnifiedAuthProvider for auth state changes
    final authSystem = ref.watch(simpleSupabaseAuthProvider.notifier);
    
    // Return a stream that emits the current user
    return Stream.value(authSystem.currentUser);
  }

  Future<void> signInWithGoogle() async {
    // The loading and error states for this specific operation should be
    // handled in the UI layer where this method is called, for example, by
    // using a separate local state variable (e.g., a Hook useState(false)
    // for isLoading) or another provider if the operation's state needs to
    // be more globally accessible.
    try {
      final authSystem = ref.read(simpleSupabaseAuthProvider.notifier);
      await authSystem.signInWithGoogle();
      // The user state will automatically update via the auth state stream
      // in build().
    } catch (e) {
      // Rethrow the error to be caught in the UI or calling function.
      // The UI can then display an appropriate error message.
      rethrow;
    }
  }

  Future<void> signOut() async {
    try {
      final authSystem = ref.read(simpleSupabaseAuthProvider.notifier);
      await authSystem.signOut();
      // The user state will automatically update to null via the
      // auth system.
    } catch (e) {
      rethrow;
    }
  }
}

// =============================================================================
// COMPATIBILITY PROVIDERS - Bridge between old and new auth systems
// =============================================================================

/// Compatibility provider that converts the new User model to the old UserModel
/// format that the account screen expects
@riverpod
Stream<UserModel?> currentUserStream(Ref ref) async* {
  final authState = ref.watch(simpleSupabaseAuthProvider);
  
  yield* authState.when(
    initial: () => Stream.value(null),
    loading: (message, operation) => Stream.value(null),
    authenticated: (user, token, refreshToken, tokenExpiry, sessionStart) async* {
      // Convert new User to old UserModel format
      final userModel = UserModel(
        id: user.id,
        authId: user.id, // Use same ID for auth
        name: user.fullName,
        email: user.email,
        phone: user.phoneNumber,
        profileImageUrl: user.avatarUrl,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        isDeleted: false,
        role: 'customer', // Default role - will be fetched from API
        isApproved: false, // Default - will be fetched from API
        isSellerRequested: false, // Default - will be fetched from API
        isActive: user.isActive,
      );
      yield userModel;
    },
    unauthenticated: (reason) async* {
      yield null;
    },
    error: (message, errorCode, errorType, isRecoverable, originalException) async* {
      yield null;
    },
    emailVerificationPending: (email, sentAt) async* {
      yield null;
    },
    sessionExpired: (expiredAt, autoRefreshAttempted) async* {
      yield null;
    },
  );
}

/// Provider for the current user as UserModel (compatibility)
@riverpod
UserModel? currentUser(Ref ref) {
  final userAsync = ref.watch(currentUserStreamProvider);
  return userAsync.value;
}

/// Provider for authentication status (compatibility)
@riverpod
bool isAuthenticated(Ref ref) {
  final authState = ref.watch(simpleSupabaseAuthProvider);
  return authState.maybeWhen(
    authenticated: (user, token, refreshToken, tokenExpiry, sessionStart) => true,
    orElse: () => false,
  );
}
