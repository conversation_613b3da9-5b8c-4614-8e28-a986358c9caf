/// شاشة إعادة تعيين كلمة المرور - Forever Plan Compliant
///
/// هذه هي الوجهة النهائية في عملية استعادة كلمة المرور.
/// بعد أن ينقر المستخدم على الرابط في بريده الإلكتروني، يتم توجيهه إلى هذه الشاشة
/// لإدخال كلمة مرور جديدة وتأكيدها.
/// 
/// Forever Plan Architecture: Flutter UI Only → Go API → Supabase Data
library;

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../../../core/utils/validators.dart';
import '../../../shared/widgets/primary_button.dart';

final _logger = Logger('ResetPasswordScreen');

class ResetPasswordScreen extends HookConsumerWidget {
  const ResetPasswordScreen({super.key, this.accessToken, this.refreshToken});

  final String? accessToken;
  final String? refreshToken;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final passwordController = useTextEditingController();
    final confirmPasswordController = useTextEditingController();
    final isLoading = useState(false);
    final isPasswordVisible = useState(false);
    final isConfirmPasswordVisible = useState(false);
    final isResetComplete = useState(false);

    Future<void> handleResetPassword() async {
      if (formKey.currentState!.validate()) {
        if (passwordController.text != confirmPasswordController.text) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('كلمات المرور غير متطابقة'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
          return;
        }

        try {
          if (context.mounted) {
            isLoading.value = true;
          }

          _logger.info('Attempting password reset');

          // Use Go API via UnifiedAuthProvider - Forever Plan Compliance
          // Flutter UI Only → Go API → Supabase Data
          final authProvider = ref.read(simpleSupabaseAuthProvider.notifier);
          await authProvider.updatePassword(
            newPassword: passwordController.text,
            accessToken: accessToken,
          );

          if (!context.mounted) return;

          isResetComplete.value = true;

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('تم إعادة تعيين كلمة المرور بنجاح'),
              backgroundColor: Theme.of(context).colorScheme.primary,
            ),
          );

          // Navigate to login screen after successful reset
          Future.delayed(const Duration(seconds: 2), () {
            if (context.mounted) {
              context.go('/auth/login');
            }
          });
        } catch (e) {
          _logger.severe('Password reset failed: $e');

          if (!context.mounted) return;

          String errorMessage = 'حدث خطأ أثناء إعادة تعيين كلمة المرور';
          if (e.toString().toLowerCase().contains('session not found')) {
            errorMessage = 'جلسة إعادة تعيين كلمة المرور منتهية الصلاحية';
          } else if (e.toString().toLowerCase().contains('weak password')) {
            errorMessage = 'كلمة المرور ضعيفة، يرجى اختيار كلمة مرور أقوى';
          } else if (e.toString().toLowerCase().contains('same password')) {
            errorMessage = 'لا يمكن استخدام كلمة المرور الحالية نفسها';
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: SelectableText.rich(
                TextSpan(
                  text: errorMessage,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onError,
                  ),
                ),
              ),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        } finally {
          if (context.mounted) {
            isLoading.value = false;
          }
        }
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('إعادة تعيين كلمة المرور'),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.lock_reset,
                        size: 80,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      
                      const SizedBox(height: 24),
                      
                      Text(
                        'إعادة تعيين كلمة المرور',
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      
                      const SizedBox(height: 16),
                      
                      Text(
                        'يرجى إدخال كلمة مرور جديدة وقوية لحسابك',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      
                      const SizedBox(height: 48),
                      
                      // Password Field
                      TextFormField(
                        controller: passwordController,
                        obscureText: !isPasswordVisible.value,
                        decoration: InputDecoration(
                          labelText: 'كلمة المرور الجديدة',
                          hintText: 'أدخل كلمة مرور قوية',
                          prefixIcon: const Icon(Icons.lock_outline),
                          suffixIcon: IconButton(
                            icon: Icon(
                              isPasswordVisible.value
                                  ? Icons.visibility_off
                                  : Icons.visibility,
                            ),
                            onPressed: () {
                              isPasswordVisible.value = !isPasswordVisible.value;
                            },
                          ),
                        ),
                        validator: Validators.validatePassword,
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Confirm Password Field
                      TextFormField(
                        controller: confirmPasswordController,
                        obscureText: !isConfirmPasswordVisible.value,
                        decoration: InputDecoration(
                          labelText: 'تأكيد كلمة المرور',
                          hintText: 'أعد إدخال كلمة المرور',
                          prefixIcon: const Icon(Icons.lock_outline),
                          suffixIcon: IconButton(
                            icon: Icon(
                              isConfirmPasswordVisible.value
                                  ? Icons.visibility_off
                                  : Icons.visibility,
                            ),
                            onPressed: () {
                              isConfirmPasswordVisible.value = !isConfirmPasswordVisible.value;
                            },
                          ),
                        ),
                        validator: (value) {
                          if (value != passwordController.text) {
                            return 'كلمات المرور غير متطابقة';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 32),
                      
                      // Reset Button
                      PrimaryButton(
                        onPressed: isResetComplete.value ? null : handleResetPassword,
                        isLoading: isLoading.value,
                        text: isResetComplete.value 
                            ? 'تم إعادة التعيين بنجاح' 
                            : 'إعادة تعيين كلمة المرور',
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Back to Login
                      TextButton(
                        onPressed: () {
                          context.go('/auth/login');
                        },
                        child: const Text('العودة إلى تسجيل الدخول'),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
