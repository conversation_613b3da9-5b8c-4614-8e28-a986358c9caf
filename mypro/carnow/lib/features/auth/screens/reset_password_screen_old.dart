/// شاشة إعادة تعيين كلمة المرور
///
/// هذه هي الوجهة النهائية في عملية استعادة كلمة المرور.
/// بعد أن ينقر المستخدم على الرابط في بريده الإلكتروني، يتم توجيهه إلى هذه الشاشة
/// لإدخال كلمة مرور جديدة وتأكيدها.
library;

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../../core/auth/unified_auth_provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/validators.dart';
import '../../../../shared/widgets/primary_button.dart';

final _logger = Logger('ResetPasswordScreen');

class ResetPasswordScreen extends HookConsumerWidget {
  const ResetPasswordScreen({super.key, this.accessToken, this.refreshToken});

  final String? accessToken;
  final String? refreshToken;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final passwordController = useTextEditingController();
    final confirmPasswordController = useTextEditingController();
    final formKey = useMemoized(GlobalKey<FormState>.new);
    final isLoading = useState(false);
    final isPasswordVisible = useState(false);
    final isConfirmPasswordVisible = useState(false);
    final isResetComplete = useState(false);

    // Check if we have valid tokens
    useEffect(() {
      if (accessToken == null || refreshToken == null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text(
                  'رابط إعادة تعيين كلمة المرور غير صالح أو منتهي الصلاحية',
                ),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
            context.go('/forgot-password');
          }
        });
      }
      return null;
    });

    Future<void> handleResetPassword() async {
      if (formKey.currentState!.validate()) {
        if (passwordController.text != confirmPasswordController.text) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('كلمات المرور غير متطابقة'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
          return;
        }

        try {
          if (context.mounted) {
            isLoading.value = true;
          }

          // Set the session first if we have tokens
          if (accessToken != null && refreshToken != null) {
            // Token handling will be done by Go API
          }

          // Use Go API via UnifiedAuthProvider - Forever Plan Compliance
          // Flutter UI Only → Go API → Supabase Data
          final authProvider = ref.read(simpleSupabaseAuthProvider.notifier);
          await authProvider.updatePassword(
            newPassword: passwordController.text,
            accessToken: accessToken,
          );

          if (!context.mounted) return;

          if (context.mounted) {
            isResetComplete.value = true;
          }
          _logger.info('Password reset successfully');
        } catch (e) {
          if (!context.mounted) return;

          String errorMessage;
          final errorString = e.toString().toLowerCase();
          
          if (errorString.contains('session not found')) {
            errorMessage = 'جلسة إعادة تعيين كلمة المرور منتهية الصلاحية';
          } else if (errorString.contains('weak password')) {
            errorMessage = 'كلمة المرور ضعيفة، يرجى اختيار كلمة مرور أقوى';
          } else if (errorString.contains('same password')) {
            errorMessage = 'لا يمكن استخدام كلمة المرور الحالية نفسها';
          } else {
            errorMessage = 'فشل في إعادة تعيين كلمة المرور: $e';
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: SelectableText.rich(
                TextSpan(
                  text: errorMessage,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onError,
                  ),
                ),
                style: TextStyle(color: Theme.of(context).colorScheme.onError),
              ),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        } finally {
          if (context.mounted) {
            isLoading.value = false;
          }
        }
      }
    }

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('إعادة تعيين كلمة المرور'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.transparent,
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppTheme.spacingL),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Logo
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Center(
                    child: Text(
                      'CarNow',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: AppTheme.spacingXL),

                if (!isResetComplete.value) ...[
                  // Title and description
                  const Text(
                    'إعادة تعيين كلمة المرور',
                    style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  Text(
                    'أدخل كلمة المرور الجديدة لحسابك',
                    style: TextStyle(
                      fontSize: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppTheme.spacingXL),

                  // Form
                  Form(
                    key: formKey,
                    child: Column(
                      children: [
                        // New Password field
                        TextFormField(
                          controller: passwordController,
                          decoration: InputDecoration(
                            labelText: 'كلمة المرور الجديدة',
                            prefixIcon: const Icon(Icons.lock_outline),
                            suffixIcon: IconButton(
                              icon: Icon(
                                isPasswordVisible.value
                                    ? Icons.visibility_off
                                    : Icons.visibility,
                              ),
                              onPressed: () => isPasswordVisible.value =
                                  !isPasswordVisible.value,
                            ),
                          ),
                          obscureText: !isPasswordVisible.value,
                          textInputAction: TextInputAction.next,
                          validator: Validators.validatePassword,
                        ),
                        const SizedBox(height: AppTheme.spacingM),

                        // Confirm Password field
                        TextFormField(
                          controller: confirmPasswordController,
                          decoration: InputDecoration(
                            labelText: 'تأكيد كلمة المرور',
                            prefixIcon: const Icon(Icons.lock_outline),
                            suffixIcon: IconButton(
                              icon: Icon(
                                isConfirmPasswordVisible.value
                                    ? Icons.visibility_off
                                    : Icons.visibility,
                              ),
                              onPressed: () => isConfirmPasswordVisible.value =
                                  !isConfirmPasswordVisible.value,
                            ),
                          ),
                          obscureText: !isConfirmPasswordVisible.value,
                          textInputAction: TextInputAction.done,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى تأكيد كلمة المرور';
                            }
                            if (value != passwordController.text) {
                              return 'كلمات المرور غير متطابقة';
                            }
                            return null;
                          },
                          onFieldSubmitted: (_) => handleResetPassword(),
                        ),
                        const SizedBox(height: AppTheme.spacingL),

                        // Password requirements
                        Container(
                          padding: const EdgeInsets.all(AppTheme.spacingM),
                          decoration: BoxDecoration(
                            color: Theme.of(
                              context,
                            ).colorScheme.surfaceContainerHighest,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'متطلبات كلمة المرور:',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: Theme.of(
                                    context,
                                  ).colorScheme.onSurfaceVariant,
                                ),
                              ),
                              const SizedBox(height: AppTheme.spacingS),
                              _buildPasswordRequirement('8 أحرف على الأقل'),
                              _buildPasswordRequirement(
                                'حرف كبير واحد على الأقل',
                              ),
                              _buildPasswordRequirement(
                                'حرف صغير واحد على الأقل',
                              ),
                              _buildPasswordRequirement('رقم واحد على الأقل'),
                            ],
                          ),
                        ),
                        const SizedBox(height: AppTheme.spacingL),

                        // Submit button
                        PrimaryButton(
                          onPressed: handleResetPassword,
                          isLoading: isLoading.value,
                          text: 'إعادة تعيين كلمة المرور',
                        ),
                      ],
                    ),
                  ),
                ] else ...[
                  // Success state
                  Container(
                    padding: const EdgeInsets.all(AppTheme.spacingL),
                    decoration: BoxDecoration(
                      color: Colors.green.withAlpha((0.1 * 255).toInt()),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.green.withAlpha((0.3 * 255).toInt()),
                      ),
                    ),
                    child: Column(
                      children: [
                        Icon(
                          Icons.check_circle_outline,
                          size: 64,
                          color: Colors.green.shade600,
                        ),
                        const SizedBox(height: AppTheme.spacingM),
                        Text(
                          'تم إعادة تعيين كلمة المرور!',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.green.shade700,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: AppTheme.spacingS),
                        Text(
                          'تم إعادة تعيين كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول بكلمة المرور الجديدة',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.green.shade600,
                            height: 1.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingL),

                  // Go to login button
                  PrimaryButton(
                    onPressed: () => context.go('/login'),
                    text: 'تسجيل الدخول',
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPasswordRequirement(String requirement) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 16,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 8),
          Text(
            requirement,
            style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }
}
