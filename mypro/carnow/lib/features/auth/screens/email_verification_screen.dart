/// شاشة التحقق من البريد الإلكتروني
///
/// تُعرض هذه الشاشة بعد أن يقوم المستخدم بإنشاء حساب جديد أو طلب إعادة تعيين كلمة المرور.
/// تُعلم المستخدم بأنه تم إرسال بريد إلكتروني إليه، وتطلب منه التحقق من صندوق الوارد
/// والنقر على الرابط المرسل لتفعيل حسابه أو للمتابعة إلى شاشة إعادة التعيين.
/// تحتوي على آلية لإعادة إرسال البريد الإلكتروني.
/// Uses UnifiedAuthProvider for ALL authentication operations
library;

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:logging/logging.dart';


import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../../../core/auth/auth_models.dart';

final _logger = Logger('EmailVerificationScreen');

/// Email Verification Screen - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// ✅ Uses UnifiedAuthProvider for authentication state management
/// ✅ Uses Go API via UnifiedAuthProvider - Forever Plan Compliant
/// ✅ Clean async/await patterns

class EmailVerificationScreen extends HookConsumerWidget {
  const EmailVerificationScreen({
    super.key,
    required this.email,
    this.isPasswordReset = false,
  });

  final String email;
  final bool isPasswordReset;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoading = useState(false);
    final canResend = useState(true);
    final resendCooldown = useState(0);

    // Watch auth state
    final authState = ref.watch(simpleSupabaseAuthProvider);

    // Cooldown timer for resend button
    useEffect(() {
      if (resendCooldown.value > 0) {
        final timer = Stream.periodic(const Duration(seconds: 1), (i) {
          final remaining = 60 - i - 1;
          if (remaining <= 0) {
            canResend.value = true;
            resendCooldown.value = 0;
            return;
          }
          resendCooldown.value = remaining;
        }).listen((_) {});

        return timer.cancel;
      }
      return null;
    }, [resendCooldown.value]);

    Future<void> resendVerificationEmail() async {
      if (!canResend.value || isLoading.value) return;

      try {
        isLoading.value = true;
        _logger.info('Resending verification email to: $email');

        if (isPasswordReset) {
          // Use Go API via UnifiedAuthProvider - Forever Plan Compliance
          final authProvider = ref.read(simpleSupabaseAuthProvider.notifier);
          await authProvider.resetPassword(email);
        } else {
          // For email verification, use Go API - Forever Plan Compliance
          final authProvider = ref.read(simpleSupabaseAuthProvider.notifier);
          await authProvider.resendVerificationEmail(email);
        }

        if (context.mounted) {
          isLoading.value = false;
          canResend.value = false;
          resendCooldown.value = 60;

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                isPasswordReset
                    ? 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني'
                    : 'تم إرسال رسالة التحقق مرة أخرى',
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        _logger.severe('Error resending email verification: $e');
        if (context.mounted) {
          isLoading.value = false;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }

    Future<void> checkEmailAndProceed() async {
      try {
        _logger.info('Checking email verification status');

        // For now, we'll assume verification is successful and navigate
        // In a real implementation, this would check the backend for verification status
        final isVerified =
            true; // Placeholder - implement actual verification check

        if (context.mounted) {
          if (isVerified) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم التحقق من البريد الإلكتروني بنجاح!'),
                backgroundColor: Colors.green,
              ),
            );

            // Navigate to appropriate screen
            if (isPasswordReset) {
              context.go('/auth/reset-password');
            } else {
              context.go('/dashboard');
            }
          }
        }
      } catch (e) {
        _logger.severe('Error checking email verification: $e');
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ في التحقق: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              Theme.of(context).colorScheme.surface,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                // App Bar
                Row(
                  children: [
                    IconButton(
                      onPressed: () => context.pop(),
                      icon: const Icon(Icons.arrow_back),
                      style: IconButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.surface,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'التحقق من البريد الإلكتروني',
                        style: Theme.of(context).textTheme.headlineSmall,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(width: 40), // Balance the back button
                  ],
                ),

                const SizedBox(height: 40),

                // Email verification content
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Email icon
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          borderRadius: BorderRadius.circular(40),
                        ),
                        child: Icon(
                          Icons.email_outlined,
                          size: 40,
                          color: Theme.of(context).colorScheme.onPrimary,
                        ),
                      ),

                      const SizedBox(height: 32),

                      // Title
                      Text(
                        isPasswordReset
                            ? 'تحقق من بريدك الإلكتروني'
                            : 'تحقق من بريدك الإلكتروني',
                        style: Theme.of(context).textTheme.headlineMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 16),

                      // Description
                      Text(
                        isPasswordReset
                            ? 'تم إرسال رابط إعادة تعيين كلمة المرور إلى:'
                            : 'تم إرسال رسالة التحقق إلى:',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 8),

                      // Email address
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Theme.of(context).colorScheme.outline,
                          ),
                        ),
                        child: Text(
                          email,
                          style: Theme.of(context).textTheme.bodyLarge
                              ?.copyWith(fontWeight: FontWeight.w600),
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Instructions
                      Text(
                        isPasswordReset
                            ? 'انقر على الرابط في البريد الإلكتروني لإعادة تعيين كلمة المرور.'
                            : 'انقر على الرابط في البريد الإلكتروني لتفعيل حسابك.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 40),

                      // Check email button
                      SizedBox(
                        width: double.infinity,
                        height: 48,
                        child: ElevatedButton(
                          onPressed: checkEmailAndProceed,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).primaryColor,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: Text(
                            isPasswordReset ? 'تم إعادة التعيين' : 'تم التحقق',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Resend button
                      SizedBox(
                        width: double.infinity,
                        height: 48,
                        child: OutlinedButton(
                          onPressed: canResend.value && !isLoading.value
                              ? resendVerificationEmail
                              : null,
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Theme.of(context).primaryColor,
                            side: BorderSide(
                              color: Theme.of(context).primaryColor,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: isLoading.value
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                              : Text(
                                  canResend.value
                                      ? 'إعادة إرسال الرسالة'
                                      : 'إعادة الإرسال بعد ${resendCooldown.value} ثانية',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                        ),
                      ),

                      const SizedBox(height: 32),

                      // Support text
                      Text(
                        'لم تتلق الرسالة؟ تحقق من مجلد الرسائل المهملة أو انتظر قليلاً ثم أعد المحاولة.',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.5),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                // Show auth state messages
                authState.when(
                  initial: () => const SizedBox.shrink(),
                  loading: (message, operation) => const Padding(
                    padding: EdgeInsets.only(top: 16),
                    child: Center(child: CircularProgressIndicator()),
                  ),
                  authenticated:
                      (user, token, refreshToken, tokenExpiry, sessionStart) =>
                          const SizedBox.shrink(),
                  unauthenticated: (reason) => const SizedBox.shrink(),
                  error:
                      (
                        message,
                        errorCode,
                        errorType,
                        isRecoverable,
                        originalException,
                      ) => Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.red[200]!),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.error_outline,
                                color: Colors.red[600],
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  message,
                                  style: TextStyle(
                                    color: Colors.red[700],
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  emailVerificationPending: (email, sentAt) =>
                      const SizedBox.shrink(),
                  sessionExpired: (expiredAt, autoRefreshAttempted) =>
                      const SizedBox.shrink(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
